from models.clip_model import CL<PERSON><PERSON>extModel
from transformers import CLIP<PERSON>okenizer, CLIPModel
from tqdm import tqdm
import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import random
from torchvision import transforms

@torch.no_grad()
def celeb_names_cross_init(
        celeb_path : str,
        tokenizer : CLIPTokenizer,
        text_encoder: CLIPTextModel,
        n_column: int=2,
    ):
    with open(celeb_path, 'r') as f:
        celeb_names=f.read().splitlines()
    # get embeddings
    col_embeddings=[[]for _ in range(n_column)]
    for name in tqdm(celeb_names,desc='get embeddings'):
        token_ids=tokenizer(
            name,
            padding="do_not_pad",
            truncation=True,
            max_length=tokenizer.model_max_length,
            return_tensors="pt",
        ).input_ids[0] # (n,)
        embeddings = text_encoder.get_input_embeddings().weight.data[token_ids] # (n,1024)

        # remove the start and end characters
        for i in range(1,min(embeddings.shape[0]-1,n_column+1)):
            col_embeddings[i-1].append(embeddings[i].unsqueeze(0))
    # mean for all names
    for i in range(n_column): 
        col_embeddings[i]=torch.cat(col_embeddings[i]).mean(dim=0).unsqueeze(0)
    col_embeddings=torch.cat(col_embeddings) #(n,1024)
    bos_embed,eos_embed,pad_embed=text_encoder.get_input_embeddings().weight.data[[tokenizer.bos_token_id,tokenizer.eos_token_id,tokenizer.pad_token_id]]
    input_embeds=torch.cat([bos_embed.unsqueeze(0),col_embeddings,eos_embed.unsqueeze(0),pad_embed.repeat(75-col_embeddings.shape[0],1)]) # (77,1024)
    # cross init
    col_embeddings=text_encoder(inputs_embeds=input_embeds.unsqueeze(0))[0][0][1:1+n_column] # (n,1024)

    return col_embeddings # (n,1024)

@torch.no_grad()
def token_cross_init(
    tokens : str|list[str],
    tokenizer : CLIPTokenizer,
    text_encoder: CLIPTextModel,
    return_first_embeds:bool=False,
):
    if isinstance(tokens,list):
        tokens=' '.join(tokens)
    
    token_ids=tokenizer(
        tokens,
        padding="do_not_pad",
        truncation=True,
        max_length=tokenizer.model_max_length,
        return_tensors="pt",
    ).input_ids.to(text_encoder.device) # (1,k)
    if return_first_embeds:
        embeds=text_encoder.get_input_embeddings().weight.data[token_ids[0]] # (k+2,1024)
    else:
        embeds=text_encoder(token_ids)[0][0] # (k+2,1024)
    return embeds[1:-1] #(k,1024)

@torch.no_grad()
def image_grid(imgs, rows, cols):
    assert len(imgs) == rows * cols
    w, h = imgs[0].size
    grid = Image.new('RGB', size=(cols * w, rows * h))
    for i, img in enumerate(imgs):
        grid.paste(img, box=(i % cols * w, i // cols * h))
    return grid

def setup_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True

from diffusers.models.attention_processor import Attention

class TransformerFusion(nn.Module):
    def __init__(self, dim=1024):
        super().__init__()
        self.attention = Attention(query_dim=dim, heads=8, dim_head=dim//8)
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.img_proj = nn.Linear(dim, dim)

    def forward(self, text_emb, img_emb):
        # Strengthen image features
        img_emb = self.img_proj(img_emb)
        img_emb = self.norm1(img_emb)

        # Use image as query, text as key/value for better identity preservation
        combined = torch.stack([img_emb, text_emb], dim=1)  # [1, 2, 1024]
        fused = self.attention(combined)

        # Weighted combination favoring image features
        result = 0.7 * fused[:, 0] + 0.3 * fused[:, 1]  # Favor image over text
        return self.norm2(result)

@torch.no_grad()
def fusion_embeddings(
    celeb_path: str,
    ref_image_path: str,
    tokenizer: CLIPTokenizer,
    text_encoder: CLIPTextModel,
    n_column: int = 2,
    fusion_weight: float = 0.5,
    use_transformer: bool = False,
):
    clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
    vision_model = clip_model.vision_model.to(text_encoder.device)

    with open(celeb_path, 'r') as f:
        celeb_names = f.read().splitlines()

    col_embeddings = [[] for _ in range(n_column)]
    for name in tqdm(celeb_names, desc='get embeddings'):
        token_ids = tokenizer(
            name,
            padding="do_not_pad",
            truncation=True,
            max_length=tokenizer.model_max_length,
            return_tensors="pt",
        ).input_ids[0]
        embeddings = text_encoder.get_input_embeddings().weight.data[token_ids]

        for i in range(1, min(embeddings.shape[0]-1, n_column+1)):
            col_embeddings[i-1].append(embeddings[i].unsqueeze(0))

    preprocess = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(
            mean=[0.48145466, 0.4578275, 0.40821073],
            std=[0.26862954, 0.26130258, 0.27577711]
        )
    ])

    image = Image.open(ref_image_path)
    if not image.mode == "RGB":
        image = image.convert("RGB")
    image = preprocess(image).unsqueeze(0).to(text_encoder.device)

    vision_outputs = vision_model(image)
    image_features = vision_outputs.last_hidden_state  # [1, 577, 768]

    # Use pooled features for stronger identity representation
    pooled_features = vision_outputs.pooler_output  # [1, 768] - global image representation

    # Project both patch and pooled features
    projection_layer = torch.nn.Linear(768, 1024).to(text_encoder.device)
    projected_image_features = projection_layer(image_features)  # [1, 577, 1024]
    projected_pooled = projection_layer(pooled_features)  # [1, 1024]

    fusion_model = TransformerFusion().to(text_encoder.device) if use_transformer else None

    for i in range(n_column):
        text_emb = torch.cat(col_embeddings[i]).mean(dim=0)

        # Use both patch features and pooled features for stronger identity
        if i < projected_image_features.shape[1] - 1:
            patch_emb = projected_image_features[0, i+1]  # Patch-level features
        else:
            patch_emb = projected_pooled[0]  # Use pooled features as fallback

        # Combine patch and pooled features for stronger identity signal
        img_emb = 0.6 * patch_emb + 0.4 * projected_pooled[0]

        if fusion_model:
            fused_emb = fusion_model(text_emb.unsqueeze(0), img_emb.unsqueeze(0)).squeeze(0)
            # Reduce fusion weight to preserve more image identity
            fused_emb = (1-fusion_weight) * fused_emb + fusion_weight * img_emb
        else:
            fused_emb = text_emb

        col_embeddings[i] = fused_emb.unsqueeze(0)

    col_embeddings = torch.cat(col_embeddings)
    bos_embed, eos_embed, pad_embed = text_encoder.get_input_embeddings().weight.data[
        [tokenizer.bos_token_id, tokenizer.eos_token_id, tokenizer.pad_token_id]
    ]
    input_embeds = torch.cat([
        bos_embed.unsqueeze(0),
        col_embeddings,
        eos_embed.unsqueeze(0),
        pad_embed.repeat(75-col_embeddings.shape[0], 1)
    ])

    col_embeddings = text_encoder(inputs_embeds=input_embeds.unsqueeze(0))[0][0][1:1+n_column]

    return col_embeddings