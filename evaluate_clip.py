import os
import argparse
import torch
import numpy as np
from PIL import Image
import clip
import json
from tqdm import tqdm
import glob

class CLIPEvaluator:
    def __init__(self, device='cuda', model_name='ViT-B/32'):
        self.device = device
        print(f"Loading CLIP model: {model_name}")
        self.model, self.preprocess = clip.load(model_name, device=device)
        self.model.eval()
        
    def compute_clip_score(self, image_path, text_prompt):
        """Compute CLIP score between image and text prompt"""
        try:
            # Load and preprocess image
            image = Image.open(image_path).convert('RGB')
            image_input = self.preprocess(image).unsqueeze(0).to(self.device)
            
            # Tokenize text
            text_input = clip.tokenize([text_prompt]).to(self.device)
            
            # Compute features
            with torch.no_grad():
                image_features = self.model.encode_image(image_input)
                text_features = self.model.encode_text(text_input)
                
                # Normalize features
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                text_features = text_features / text_features.norm(dim=-1, keepdim=True)
                
                # Compute similarity
                similarity = torch.cosine_similarity(image_features, text_features).item()
                
            return similarity
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            return 0.0
    
    def evaluate_folder(self, generated_folder_path, text_prompt, 
                       score_threshold=0.2, top_k=None, filter_list=None):
        """
        Evaluate prompt similarity for all images in a folder
        
        Args:
            generated_folder_path: Path to folder containing generated images
            text_prompt: Text prompt to compare against
            score_threshold: Minimum CLIP score to consider as "good"
            top_k: If specified, only evaluate top-k highest scoring images
            filter_list: List of specific image filenames to evaluate (optional)
        """
        # Get all image files in the folder
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(generated_folder_path, ext)))
            image_files.extend(glob.glob(os.path.join(generated_folder_path, ext.upper())))
        
        # Filter by specific list if provided
        if filter_list is not None:
            filter_basenames = [os.path.basename(f) for f in filter_list]
            image_files = [f for f in image_files if os.path.basename(f) in filter_basenames]
        
        if len(image_files) == 0:
            print(f"No images found in {generated_folder_path}")
            return None
        
        print(f"Found {len(image_files)} images to evaluate")
        print(f"Text prompt: '{text_prompt}'")
        
        results = []
        valid_scores = []
        
        for img_path in tqdm(image_files, desc="Computing CLIP scores"):
            score = self.compute_clip_score(img_path, text_prompt)
            
            results.append({
                'image_path': img_path,
                'clip_score': score
            })
            
            valid_scores.append(score)
        
        # Sort by CLIP score (descending)
        results.sort(key=lambda x: x['clip_score'], reverse=True)
        
        # Filter by threshold or top-k
        filtered_results = []
        if top_k is not None:
            filtered_results = results[:top_k]
            print(f"Selected top-{top_k} images")
        else:
            filtered_results = [r for r in results if r['clip_score'] >= score_threshold]
            print(f"Selected {len(filtered_results)} images with CLIP score >= {score_threshold}")
        
        # Compute statistics
        if valid_scores:
            stats = {
                'total_images': len(image_files),
                'selected_images': len(filtered_results),
                'mean_clip_score': np.mean(valid_scores),
                'std_clip_score': np.std(valid_scores),
                'max_clip_score': np.max(valid_scores),
                'min_clip_score': np.min(valid_scores),
                'selected_mean_clip_score': np.mean([r['clip_score'] for r in filtered_results]) if filtered_results else 0.0
            }
        else:
            stats = {
                'total_images': len(image_files),
                'selected_images': 0,
                'mean_clip_score': 0.0,
                'std_clip_score': 0.0,
                'max_clip_score': 0.0,
                'min_clip_score': 0.0,
                'selected_mean_clip_score': 0.0
            }
        
        return {
            'results': results,
            'filtered_results': filtered_results,
            'statistics': stats,
            'text_prompt': text_prompt
        }

def main():
    parser = argparse.ArgumentParser(description='Evaluate prompt similarity using CLIP')
    parser.add_argument('--generated_folder', type=str, required=True,
                       help='Path to folder containing generated images')
    parser.add_argument('--text_prompt', type=str, required=True,
                       help='Text prompt to evaluate against')
    parser.add_argument('--output_file', type=str, default='clip_results.json',
                       help='Output JSON file for results')
    parser.add_argument('--score_threshold', type=float, default=0.2,
                       help='Minimum CLIP score threshold for filtering')
    parser.add_argument('--top_k', type=int, default=None,
                       help='Select top-k highest scoring images (overrides threshold)')
    parser.add_argument('--filter_file', type=str, default=None,
                       help='JSON file containing list of images to evaluate (from ArcFace results)')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    parser.add_argument('--clip_model', type=str, default='ViT-B/32',
                       help='CLIP model to use (ViT-B/32, ViT-B/16, ViT-L/14)')
    
    args = parser.parse_args()
    
    # Load filter list if provided
    filter_list = None
    if args.filter_file and os.path.exists(args.filter_file):
        print(f"Loading filter list from: {args.filter_file}")
        with open(args.filter_file, 'r') as f:
            filter_data = json.load(f)
            if 'filtered_results' in filter_data:
                filter_list = [r['image_path'] for r in filter_data['filtered_results']]
                print(f"Loaded {len(filter_list)} images from filter file")
    
    # Initialize evaluator
    evaluator = CLIPEvaluator(device=args.device, model_name=args.clip_model)
    
    # Run evaluation
    results = evaluator.evaluate_folder(
        args.generated_folder,
        args.text_prompt,
        args.score_threshold,
        args.top_k,
        filter_list
    )
    
    if results is None:
        print("Evaluation failed")
        return
    
    # Print statistics
    stats = results['statistics']
    print("\n" + "="*50)
    print("CLIP PROMPT SIMILARITY EVALUATION RESULTS")
    print("="*50)
    print(f"Text prompt: '{results['text_prompt']}'")
    print(f"Total images: {stats['total_images']}")
    print(f"Selected images: {stats['selected_images']}")
    print(f"Mean CLIP score (all): {stats['mean_clip_score']:.4f}")
    print(f"Selected mean CLIP score: {stats['selected_mean_clip_score']:.4f}")
    print(f"Max CLIP score: {stats['max_clip_score']:.4f}")
    print(f"Min CLIP score: {stats['min_clip_score']:.4f}")
    print(f"Std CLIP score: {stats['std_clip_score']:.4f}")
    
    # Save results
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to: {args.output_file}")
    
    # Print top 5 best images
    print(f"\nTop 5 best images:")
    for i, result in enumerate(results['results'][:5]):
        print(f"{i+1}. {os.path.basename(result['image_path'])}: {result['clip_score']:.4f}")

if __name__ == "__main__":
    main()
