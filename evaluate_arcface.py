import os
import argparse
import torch
import numpy as np
from PIL import Image
import cv2
from torchvision import transforms
import insightface
from insightface.app import FaceAnalysis
import json
from tqdm import tqdm
import glob

class ArcFaceEvaluator:
    def __init__(self, device='cuda'):
        self.device = device
        # Initialize InsightFace ArcFace model
        self.app = FaceAnalysis(providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
        self.app.prepare(ctx_id=0, det_size=(640, 640))
        
    def extract_face_embedding(self, image_path):
        """Extract face embedding using ArcFace"""
        try:
            img = cv2.imread(image_path)
            if img is None:
                print(f"Warning: Could not load image {image_path}")
                return None
                
            faces = self.app.get(img)
            if len(faces) == 0:
                print(f"Warning: No face detected in {image_path}")
                return None
                
            # Use the largest face if multiple faces detected
            face = max(faces, key=lambda x: x.bbox[2] * x.bbox[3])
            return face.embedding
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            return None
    
    def compute_similarity(self, emb1, emb2):
        """Compute cosine similarity between two embeddings"""
        if emb1 is None or emb2 is None:
            return 0.0
        
        # Normalize embeddings
        emb1 = emb1 / np.linalg.norm(emb1)
        emb2 = emb2 / np.linalg.norm(emb2)
        
        # Compute cosine similarity
        similarity = np.dot(emb1, emb2)
        return float(similarity)
    
    def evaluate_folder(self, reference_image_path, generated_folder_path, 
                       similarity_threshold=0.3, top_k=None):
        """
        Evaluate identity preservation for all images in a folder
        
        Args:
            reference_image_path: Path to reference identity image
            generated_folder_path: Path to folder containing generated images
            similarity_threshold: Minimum similarity to consider as "good"
            top_k: If specified, only evaluate top-k most similar images
        """
        print(f"Extracting reference embedding from: {reference_image_path}")
        ref_embedding = self.extract_face_embedding(reference_image_path)
        
        if ref_embedding is None:
            print("Error: Could not extract reference embedding")
            return None
        
        # Get all image files in the folder
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
        image_files = []
        for ext in image_extensions:
            image_files.extend(glob.glob(os.path.join(generated_folder_path, ext)))
            image_files.extend(glob.glob(os.path.join(generated_folder_path, ext.upper())))
        
        if len(image_files) == 0:
            print(f"No images found in {generated_folder_path}")
            return None
        
        print(f"Found {len(image_files)} images to evaluate")
        
        results = []
        valid_similarities = []
        
        for img_path in tqdm(image_files, desc="Computing similarities"):
            gen_embedding = self.extract_face_embedding(img_path)
            similarity = self.compute_similarity(ref_embedding, gen_embedding)
            
            results.append({
                'image_path': img_path,
                'similarity': similarity,
                'has_face': gen_embedding is not None
            })
            
            if gen_embedding is not None:
                valid_similarities.append(similarity)
        
        # Sort by similarity (descending)
        results.sort(key=lambda x: x['similarity'], reverse=True)
        
        # Filter by threshold or top-k
        filtered_results = []
        if top_k is not None:
            filtered_results = results[:top_k]
            print(f"Selected top-{top_k} images")
        else:
            filtered_results = [r for r in results if r['similarity'] >= similarity_threshold]
            print(f"Selected {len(filtered_results)} images with similarity >= {similarity_threshold}")
        
        # Compute statistics
        if valid_similarities:
            stats = {
                'total_images': len(image_files),
                'images_with_faces': len(valid_similarities),
                'selected_images': len(filtered_results),
                'mean_similarity': np.mean(valid_similarities),
                'std_similarity': np.std(valid_similarities),
                'max_similarity': np.max(valid_similarities),
                'min_similarity': np.min(valid_similarities),
                'selected_mean_similarity': np.mean([r['similarity'] for r in filtered_results if r['has_face']]) if filtered_results else 0.0
            }
        else:
            stats = {
                'total_images': len(image_files),
                'images_with_faces': 0,
                'selected_images': 0,
                'mean_similarity': 0.0,
                'std_similarity': 0.0,
                'max_similarity': 0.0,
                'min_similarity': 0.0,
                'selected_mean_similarity': 0.0
            }
        
        return {
            'results': results,
            'filtered_results': filtered_results,
            'statistics': stats
        }

def main():
    parser = argparse.ArgumentParser(description='Evaluate identity preservation using ArcFace')
    parser.add_argument('--reference_image', type=str, required=True,
                       help='Path to reference identity image')
    parser.add_argument('--generated_folder', type=str, required=True,
                       help='Path to folder containing generated images')
    parser.add_argument('--output_file', type=str, default='arcface_results.json',
                       help='Output JSON file for results')
    parser.add_argument('--similarity_threshold', type=float, default=0.3,
                       help='Minimum similarity threshold for filtering')
    parser.add_argument('--top_k', type=int, default=None,
                       help='Select top-k most similar images (overrides threshold)')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use (cuda/cpu)')
    
    args = parser.parse_args()
    
    # Initialize evaluator
    evaluator = ArcFaceEvaluator(device=args.device)
    
    # Run evaluation
    results = evaluator.evaluate_folder(
        args.reference_image,
        args.generated_folder,
        args.similarity_threshold,
        args.top_k
    )
    
    if results is None:
        print("Evaluation failed")
        return
    
    # Print statistics
    stats = results['statistics']
    print("\n" + "="*50)
    print("ARCFACE IDENTITY EVALUATION RESULTS")
    print("="*50)
    print(f"Total images: {stats['total_images']}")
    print(f"Images with detected faces: {stats['images_with_faces']}")
    print(f"Selected images: {stats['selected_images']}")
    print(f"Mean similarity (all): {stats['mean_similarity']:.4f}")
    print(f"Selected mean similarity: {stats['selected_mean_similarity']:.4f}")
    print(f"Max similarity: {stats['max_similarity']:.4f}")
    print(f"Min similarity: {stats['min_similarity']:.4f}")
    print(f"Std similarity: {stats['std_similarity']:.4f}")
    
    # Save results
    with open(args.output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nDetailed results saved to: {args.output_file}")
    
    # Print top 5 best images
    print(f"\nTop 5 best images:")
    for i, result in enumerate(results['results'][:5]):
        print(f"{i+1}. {os.path.basename(result['image_path'])}: {result['similarity']:.4f}")

if __name__ == "__main__":
    main()
