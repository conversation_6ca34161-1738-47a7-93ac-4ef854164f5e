import os
import argparse
import json
import subprocess
from pathlib import Path
import pandas as pd

def run_arcface_evaluation(reference_image, generated_folder, output_file, 
                          similarity_threshold=0.3, top_k=None):
    """Run ArcFace evaluation"""
    cmd = [
        'python', 'evaluate_arcface.py',
        '--reference_image', reference_image,
        '--generated_folder', generated_folder,
        '--output_file', output_file,
        '--similarity_threshold', str(similarity_threshold)
    ]
    
    if top_k is not None:
        cmd.extend(['--top_k', str(top_k)])
    
    print(f"Running ArcFace evaluation for: {generated_folder}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Error in ArcFace evaluation: {result.stderr}")
        return None
    
    return output_file

def run_clip_evaluation(generated_folder, text_prompt, output_file,
                       score_threshold=0.2, top_k=None, filter_file=None):
    """Run CLIP evaluation"""
    cmd = [
        'python', 'evaluate_clip.py',
        '--generated_folder', generated_folder,
        '--text_prompt', text_prompt,
        '--output_file', output_file,
        '--score_threshold', str(score_threshold)
    ]
    
    if top_k is not None:
        cmd.extend(['--top_k', str(top_k)])
    
    if filter_file is not None:
        cmd.extend(['--filter_file', filter_file])
    
    print(f"Running CLIP evaluation for: {generated_folder}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Error in CLIP evaluation: {result.stderr}")
        return None
    
    return output_file

def batch_evaluate_folders(base_folder, reference_image, prompt_template, 
                          output_dir, arcface_threshold=0.3, clip_threshold=0.2,
                          top_k_arcface=None, top_k_clip=None):
    """
    Batch evaluate multiple folders
    
    Args:
        base_folder: Base folder containing subfolders with generated images
        reference_image: Path to reference identity image
        prompt_template: Template for prompts (e.g., "a photo of {} eating cake")
        output_dir: Directory to save evaluation results
        arcface_threshold: ArcFace similarity threshold
        clip_threshold: CLIP score threshold
        top_k_arcface: Top-k for ArcFace filtering
        top_k_clip: Top-k for CLIP filtering
    """
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all subfolders
    subfolders = [f for f in os.listdir(base_folder) 
                 if os.path.isdir(os.path.join(base_folder, f))]
    
    if len(subfolders) == 0:
        print(f"No subfolders found in {base_folder}")
        return
    
    print(f"Found {len(subfolders)} folders to evaluate")
    
    results_summary = []
    
    for folder_name in subfolders:
        folder_path = os.path.join(base_folder, folder_name)
        print(f"\n{'='*60}")
        print(f"Evaluating folder: {folder_name}")
        print(f"{'='*60}")
        
        # Generate text prompt (replace {} with placeholder)
        text_prompt = prompt_template.format("person")
        
        # File paths for results
        arcface_output = os.path.join(output_dir, f"{folder_name}_arcface.json")
        clip_output = os.path.join(output_dir, f"{folder_name}_clip.json")
        
        # Run ArcFace evaluation
        arcface_file = run_arcface_evaluation(
            reference_image, folder_path, arcface_output,
            arcface_threshold, top_k_arcface
        )
        
        if arcface_file is None:
            print(f"Skipping CLIP evaluation for {folder_name} due to ArcFace failure")
            continue
        
        # Run CLIP evaluation (optionally filtered by ArcFace results)
        clip_file = run_clip_evaluation(
            folder_path, text_prompt, clip_output,
            clip_threshold, top_k_clip, arcface_file
        )
        
        # Load and summarize results
        if arcface_file and clip_file and os.path.exists(arcface_file) and os.path.exists(clip_file):
            with open(arcface_file, 'r') as f:
                arcface_data = json.load(f)
            
            with open(clip_file, 'r') as f:
                clip_data = json.load(f)
            
            summary = {
                'folder_name': folder_name,
                'text_prompt': text_prompt,
                'total_images': arcface_data['statistics']['total_images'],
                'images_with_faces': arcface_data['statistics']['images_with_faces'],
                'arcface_selected': arcface_data['statistics']['selected_images'],
                'clip_selected': clip_data['statistics']['selected_images'],
                'mean_arcface_similarity': arcface_data['statistics']['mean_similarity'],
                'selected_arcface_similarity': arcface_data['statistics']['selected_mean_similarity'],
                'mean_clip_score': clip_data['statistics']['mean_clip_score'],
                'selected_clip_score': clip_data['statistics']['selected_mean_clip_score'],
                'max_arcface_similarity': arcface_data['statistics']['max_similarity'],
                'max_clip_score': clip_data['statistics']['max_clip_score']
            }
            
            results_summary.append(summary)
            
            print(f"Results for {folder_name}:")
            print(f"  ArcFace similarity: {summary['selected_arcface_similarity']:.4f}")
            print(f"  CLIP score: {summary['selected_clip_score']:.4f}")
            print(f"  Selected images: {summary['clip_selected']}")
    
    # Save summary
    summary_file = os.path.join(output_dir, "evaluation_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    # Create CSV summary
    if results_summary:
        df = pd.DataFrame(results_summary)
        csv_file = os.path.join(output_dir, "evaluation_summary.csv")
        df.to_csv(csv_file, index=False)
        
        print(f"\n{'='*60}")
        print("BATCH EVALUATION SUMMARY")
        print(f"{'='*60}")
        print(df.to_string(index=False))
        print(f"\nSummary saved to: {summary_file}")
        print(f"CSV saved to: {csv_file}")

def main():
    parser = argparse.ArgumentParser(description='Batch evaluate multiple folders')
    parser.add_argument('--base_folder', type=str, required=True,
                       help='Base folder containing subfolders with generated images')
    parser.add_argument('--reference_image', type=str, required=True,
                       help='Path to reference identity image')
    parser.add_argument('--prompt_template', type=str, 
                       default="a photo of {} eating birthday cake",
                       help='Prompt template (use {} as placeholder)')
    parser.add_argument('--output_dir', type=str, default='./evaluation_results',
                       help='Directory to save evaluation results')
    parser.add_argument('--arcface_threshold', type=float, default=0.3,
                       help='ArcFace similarity threshold')
    parser.add_argument('--clip_threshold', type=float, default=0.2,
                       help='CLIP score threshold')
    parser.add_argument('--top_k_arcface', type=int, default=None,
                       help='Top-k for ArcFace filtering')
    parser.add_argument('--top_k_clip', type=int, default=None,
                       help='Top-k for CLIP filtering')
    
    args = parser.parse_args()
    
    batch_evaluate_folders(
        args.base_folder,
        args.reference_image,
        args.prompt_template,
        args.output_dir,
        args.arcface_threshold,
        args.clip_threshold,
        args.top_k_arcface,
        args.top_k_clip
    )

if __name__ == "__main__":
    main()
